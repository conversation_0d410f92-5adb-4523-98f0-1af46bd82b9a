/// Tests for Properties Generator functionality
///
/// This test suite validates the key.properties file generation module,
/// including file creation, content validation, and error handling.

import 'dart:io';
import 'package:test/test.dart';
import 'package:path/path.dart' as path;
import 'package:flutlock/flutlock.dart';

void main() {
  group('PropertiesGenerator', () {
    late Directory tempDir;
    late String testProjectPath;
    late String androidDir;

    setUp(() async {
      // Create temporary directory for test projects
      tempDir = await Directory.systemTemp.createTemp('flutlock_props_test_');
      testProjectPath = tempDir.path;
      androidDir = path.join(testProjectPath, 'android');

      // Create android directory structure
      await Directory(androidDir).create(recursive: true);
    });

    tearDown(() async {
      // Clean up temporary directory
      if (await tempDir.exists()) {
        await tempDir.delete(recursive: true);
      }
    });

    group('KeyPropertiesConfig', () {
      test('should create config with all parameters', () {
        const config = KeyPropertiesConfig(
          keystorePath: '/path/to/keystore.jks',
          alias: 'upload',
          storePassword: 'store_pass',
          keyPassword: 'key_pass',
          storeType: 'JKS',
        );

        expect(config.keystorePath, equals('/path/to/keystore.jks'));
        expect(config.alias, equals('upload'));
        expect(config.storePassword, equals('store_pass'));
        expect(config.keyPassword, equals('key_pass'));
        expect(config.storeType, equals('JKS'));
      });

      test('should create config from keystore configuration map', () {
        final keystoreConfig = {
          'alias': 'test_alias',
          'store_password': 'test_store_pass',
          'key_password': 'test_key_pass',
          'store_type': 'PKCS12',
        };

        final config = KeyPropertiesConfig.fromKeystoreConfig(
          keystoreConfig,
          '/test/keystore.p12',
        );

        expect(config.keystorePath, equals('/test/keystore.p12'));
        expect(config.alias, equals('test_alias'));
        expect(config.storePassword, equals('test_store_pass'));
        expect(config.keyPassword, equals('test_key_pass'));
        expect(config.storeType, equals('PKCS12'));
      });

      test('should use defaults for missing configuration values', () {
        final keystoreConfig = <String, dynamic>{};

        final config = KeyPropertiesConfig.fromKeystoreConfig(
          keystoreConfig,
          '/test/keystore.jks',
        );

        expect(config.keystorePath, equals('/test/keystore.jks'));
        expect(config.alias, equals('upload')); // default
        expect(config.storePassword, equals(''));
        expect(config.keyPassword, equals(''));
        expect(config.storeType, isNull);
      });

      test('should support copyWith method', () {
        const original = KeyPropertiesConfig(
          keystorePath: '/original/path',
          alias: 'original_alias',
          storePassword: 'original_store',
          keyPassword: 'original_key',
        );

        final modified = original.copyWith(
          keystorePath: '/new/path',
          alias: 'new_alias',
        );

        expect(modified.keystorePath, equals('/new/path'));
        expect(modified.alias, equals('new_alias'));
        expect(modified.storePassword, equals('original_store')); // unchanged
        expect(modified.keyPassword, equals('original_key')); // unchanged
      });
    });

    group('Key properties file creation', () {
      test('should create key.properties file with correct content', () async {
        const config = KeyPropertiesConfig(
          keystorePath: '../keystore.jks',
          alias: 'upload',
          storePassword: 'test_store_password',
          keyPassword: 'test_key_password',
        );

        final result = await PropertiesGenerator.createKeyProperties(
          testProjectPath,
          config,
          backup: false,
        );

        expect(result.success, isTrue);
        expect(result.errorMessage, isNull);

        // Verify file was created
        final keyPropertiesFile = File(path.join(androidDir, 'key.properties'));
        expect(await keyPropertiesFile.exists(), isTrue);

        // Verify content
        final content = await keyPropertiesFile.readAsString();
        expect(content, contains('storePassword=test_store_password'));
        expect(content, contains('keyPassword=test_key_password'));
        expect(content, contains('keyAlias=upload'));
        expect(content, contains('storeFile=../keystore.jks'));
        expect(content, contains('# Generated by FlutLock'));
      });

      test('should handle Windows path separators correctly', () async {
        const config = KeyPropertiesConfig(
          keystorePath: r'C:\path\to\keystore.jks',
          alias: 'upload',
          storePassword: 'test_pass',
          keyPassword: 'test_pass',
        );

        final result = await PropertiesGenerator.createKeyProperties(
          testProjectPath,
          config,
          backup: false,
        );

        expect(result.success, isTrue);

        // Verify content uses forward slashes
        final keyPropertiesFile = File(path.join(androidDir, 'key.properties'));
        final content = await keyPropertiesFile.readAsString();
        expect(content, contains('storeFile=C:/path/to/keystore.jks'));
        expect(content, isNot(contains(r'C:\path\to\keystore.jks')));
      });

      test('should include store type when provided', () async {
        const config = KeyPropertiesConfig(
          keystorePath: 'keystore.p12',
          alias: 'upload',
          storePassword: 'test_pass',
          keyPassword: 'test_pass',
          storeType: 'PKCS12',
        );

        final result = await PropertiesGenerator.createKeyProperties(
          testProjectPath,
          config,
          backup: false,
        );

        expect(result.success, isTrue);

        final keyPropertiesFile = File(path.join(androidDir, 'key.properties'));
        final content = await keyPropertiesFile.readAsString();
        expect(content, contains('storeType=PKCS12'));
      });

      test('should backup existing key.properties file', () async {
        // Create existing key.properties file
        final existingFile = File(path.join(androidDir, 'key.properties'));
        await existingFile.writeAsString('existing content');

        const config = KeyPropertiesConfig(
          keystorePath: 'keystore.jks',
          alias: 'upload',
          storePassword: 'test_pass',
          keyPassword: 'test_pass',
        );

        final result = await PropertiesGenerator.createKeyProperties(
          testProjectPath,
          config,
          backup: true,
        );

        expect(result.success, isTrue);
        expect(result.wasBackedUp, isTrue);

        // Verify backup file exists
        final backupFiles = await Directory(androidDir)
            .list()
            .where((entity) => entity.path.contains('key.properties.backup'))
            .toList();
        expect(backupFiles, hasLength(1));
      });

      test('should fail when android directory does not exist', () async {
        // Use a path without android directory
        final invalidProjectPath = path.join(tempDir.path, 'invalid_project');
        await Directory(invalidProjectPath).create();

        const config = KeyPropertiesConfig(
          keystorePath: 'keystore.jks',
          alias: 'upload',
          storePassword: 'test_pass',
          keyPassword: 'test_pass',
        );

        expect(
          () => PropertiesGenerator.createKeyProperties(
              invalidProjectPath, config),
          throwsA(isA<FlutLockException>()),
        );
      });
    });

    group('Path calculation', () {
      test('should calculate relative path correctly', () async {
        // Create keystore in project root
        final keystorePath = path.join(testProjectPath, 'keystore.jks');

        const config = KeyPropertiesConfig(
          keystorePath: 'will_be_replaced',
          alias: 'upload',
          storePassword: 'test_pass',
          keyPassword: 'test_pass',
        );

        final configWithPath = config.copyWith(keystorePath: keystorePath);

        final result = await PropertiesGenerator.createKeyProperties(
          testProjectPath,
          configWithPath,
          backup: false,
        );

        expect(result.success, isTrue);

        final keyPropertiesFile = File(path.join(androidDir, 'key.properties'));
        final content = await keyPropertiesFile.readAsString();
        expect(content, contains('storeFile=../keystore.jks'));
      });

      test(
          'should use absolute path when relative path has too many parent directories',
          () async {
        // Create a deeply nested keystore path
        final deepKeystorePath = path.join(
          tempDir.parent.parent.parent.parent.path,
          'deep',
          'keystore.jks',
        );

        const config = KeyPropertiesConfig(
          keystorePath: 'will_be_replaced',
          alias: 'upload',
          storePassword: 'test_pass',
          keyPassword: 'test_pass',
        );

        final configWithPath = config.copyWith(keystorePath: deepKeystorePath);

        final result = await PropertiesGenerator.createKeyProperties(
          testProjectPath,
          configWithPath,
          backup: false,
        );

        expect(result.success, isTrue);

        final keyPropertiesFile = File(path.join(androidDir, 'key.properties'));
        final content = await keyPropertiesFile.readAsString();
        // Should use absolute path due to too many parent directories
        // Path separators should be converted to forward slashes
        final expectedPath = deepKeystorePath.replaceAll(r'\', '/');
        expect(content, contains('storeFile=$expectedPath'));
      });
    });

    group('Validation and utilities', () {
      test('should validate existing key.properties file', () async {
        // Create valid key.properties file
        final keyPropertiesFile = File(path.join(androidDir, 'key.properties'));
        await keyPropertiesFile.writeAsString('''
storePassword=test_pass
keyPassword=test_pass
keyAlias=upload
storeFile=keystore.jks
''');

        final isValid =
            await PropertiesGenerator.validateKeyProperties(testProjectPath);
        expect(isValid, isTrue);
      });

      test('should detect missing key.properties file', () async {
        final isValid =
            await PropertiesGenerator.validateKeyProperties(testProjectPath);
        expect(isValid, isFalse);
      });

      test('should detect invalid key.properties file', () async {
        // Create invalid key.properties file (missing required properties)
        final keyPropertiesFile = File(path.join(androidDir, 'key.properties'));
        await keyPropertiesFile.writeAsString('''
storePassword=test_pass
# Missing other required properties
''');

        final isValid =
            await PropertiesGenerator.validateKeyProperties(testProjectPath);
        expect(isValid, isFalse);
      });

      test('should read key.properties file as map', () async {
        // Create key.properties file
        final keyPropertiesFile = File(path.join(androidDir, 'key.properties'));
        await keyPropertiesFile.writeAsString('''
# Comment line
storePassword=test_store_pass
keyPassword=test_key_pass
keyAlias=upload
storeFile=keystore.jks

# Another comment
storeType=JKS
''');

        final properties =
            await PropertiesGenerator.readKeyProperties(testProjectPath);

        expect(properties, isNotNull);
        expect(properties!['storePassword'], equals('test_store_pass'));
        expect(properties['keyPassword'], equals('test_key_pass'));
        expect(properties['keyAlias'], equals('upload'));
        expect(properties['storeFile'], equals('keystore.jks'));
        expect(properties['storeType'], equals('JKS'));
      });

      test('should check if key.properties exists', () async {
        // Initially should not exist
        expect(await PropertiesGenerator.keyPropertiesExists(testProjectPath),
            isFalse);

        // Create the file
        final keyPropertiesFile = File(path.join(androidDir, 'key.properties'));
        await keyPropertiesFile.writeAsString('test content');

        // Now should exist
        expect(await PropertiesGenerator.keyPropertiesExists(testProjectPath),
            isTrue);
      });

      test('should return correct key.properties path', () {
        final expectedPath =
            path.join(testProjectPath, 'android', 'key.properties');
        final actualPath =
            PropertiesGenerator.getKeyPropertiesPath(testProjectPath);
        expect(actualPath, equals(expectedPath));
      });
    });
  });
}
