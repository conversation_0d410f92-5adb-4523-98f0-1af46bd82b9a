{"roots": ["flutlock_example"], "packages": [{"name": "flutlock_example", "version": "1.0.0", "dependencies": ["flutlock", "path"], "devDependencies": ["lints", "test"]}, {"name": "flutlock", "version": "1.0.0", "dependencies": ["args", "json_schema", "logging", "meta", "path", "yaml"]}, {"name": "lints", "version": "2.1.1", "dependencies": []}, {"name": "yaml", "version": "3.1.3", "dependencies": ["collection", "source_span", "string_scanner"]}, {"name": "logging", "version": "1.3.0", "dependencies": []}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "meta", "version": "1.17.0", "dependencies": []}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "json_schema", "version": "5.2.1", "dependencies": ["collection", "http", "logging", "rfc_6901", "uri"]}, {"name": "rfc_6901", "version": "0.2.0", "dependencies": []}, {"name": "uri", "version": "1.0.0", "dependencies": ["matcher", "quiver"]}, {"name": "quiver", "version": "3.2.2", "dependencies": ["matcher"]}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "test_api", "version": "0.7.7", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "http", "version": "1.4.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "test", "version": "1.26.3", "dependencies": ["analyzer", "async", "boolean_selector", "collection", "coverage", "http_multi_server", "io", "js", "matcher", "node_preamble", "package_config", "path", "pool", "shelf", "shelf_packages_handler", "shelf_static", "shelf_web_socket", "source_span", "stack_trace", "stream_channel", "test_api", "test_core", "typed_data", "web_socket_channel", "webkit_inspection_protocol", "yaml"]}, {"name": "test_core", "version": "0.6.12", "dependencies": ["analyzer", "args", "async", "boolean_selector", "collection", "coverage", "frontend_server_client", "glob", "io", "meta", "package_config", "path", "pool", "source_map_stack_trace", "source_maps", "source_span", "stack_trace", "stream_channel", "test_api", "vm_service", "yaml"]}, {"name": "pool", "version": "1.5.1", "dependencies": ["async", "stack_trace"]}, {"name": "frontend_server_client", "version": "4.0.0", "dependencies": ["async", "path"]}, {"name": "shelf_packages_handler", "version": "3.0.2", "dependencies": ["path", "shelf", "shelf_static"]}, {"name": "node_preamble", "version": "2.0.2", "dependencies": []}, {"name": "source_map_stack_trace", "version": "2.1.2", "dependencies": ["path", "source_maps", "stack_trace"]}, {"name": "source_maps", "version": "0.10.13", "dependencies": ["source_span"]}, {"name": "webkit_inspection_protocol", "version": "1.2.1", "dependencies": ["logging"]}, {"name": "shelf_static", "version": "1.1.3", "dependencies": ["convert", "http_parser", "mime", "path", "shelf"]}, {"name": "package_config", "version": "2.2.0", "dependencies": ["path"]}, {"name": "io", "version": "1.0.5", "dependencies": ["meta", "path", "string_scanner"]}, {"name": "http_multi_server", "version": "3.2.2", "dependencies": ["async"]}, {"name": "convert", "version": "3.1.2", "dependencies": ["typed_data"]}, {"name": "js", "version": "0.7.2", "dependencies": []}, {"name": "glob", "version": "2.1.3", "dependencies": ["async", "collection", "file", "path", "string_scanner"]}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "shelf_web_socket", "version": "3.0.0", "dependencies": ["shelf", "stream_channel", "web_socket_channel"]}, {"name": "mime", "version": "2.0.0", "dependencies": []}, {"name": "shelf", "version": "1.4.2", "dependencies": ["async", "collection", "http_parser", "path", "stack_trace", "stream_channel"]}, {"name": "web_socket_channel", "version": "3.0.3", "dependencies": ["async", "crypto", "stream_channel", "web", "web_socket"]}, {"name": "web_socket", "version": "1.0.1", "dependencies": ["web"]}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "coverage", "version": "1.15.0", "dependencies": ["args", "cli_config", "glob", "logging", "meta", "package_config", "path", "source_maps", "stack_trace", "vm_service", "yaml"]}, {"name": "cli_config", "version": "0.2.0", "dependencies": ["args", "yaml"]}, {"name": "vm_service", "version": "15.0.2", "dependencies": []}, {"name": "analyzer", "version": "8.0.0", "dependencies": ["_fe_analyzer_shared", "collection", "convert", "crypto", "glob", "meta", "package_config", "path", "pub_semver", "source_span", "watcher", "yaml"]}, {"name": "_fe_analyzer_shared", "version": "86.0.0", "dependencies": ["meta"]}, {"name": "watcher", "version": "1.1.2", "dependencies": ["async", "path"]}, {"name": "pub_semver", "version": "2.2.0", "dependencies": ["collection"]}], "configVersion": 1}